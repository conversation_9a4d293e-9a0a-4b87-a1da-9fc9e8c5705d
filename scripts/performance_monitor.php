<?php
/**
 * Performance Monitor Script
 * 
 * This script helps identify slow queries and performance issues in the CMS system.
 * Run this script to check for potential performance problems.
 */

require_once __DIR__ . '/../bootstrap.php';

class PerformanceMonitor
{
    private $entityManager;
    private $connection;
    
    public function __construct($entityManager)
    {
        $this->entityManager = $entityManager;
        $this->connection = $entityManager->getConnection();
    }
    
    /**
     * Check for tables without proper indexes
     */
    public function checkMissingIndexes()
    {
        echo "=== Checking for Missing Indexes ===\n";
        
        $tables = [
            'cms2_customers' => ['dtc', 'email', 'statusId', 'isIdCheckRequired'],
            'cms2_company' => ['dtc', 'customerId', 'incorporationDate', 'deleted'],
            'cms2_cashbacks' => ['customerId', 'statusId', 'dtc'],
            'cms2_orders' => ['customerId', 'dtc'],
            'cms2_transactions' => ['customerId', 'dtc', 'tokenId']
        ];
        
        foreach ($tables as $table => $columns) {
            echo "\nChecking table: $table\n";
            $this->checkTableIndexes($table, $columns);
        }
    }
    
    private function checkTableIndexes($table, $requiredColumns)
    {
        try {
            $indexes = $this->connection->executeQuery("SHOW INDEX FROM $table")->fetchAllAssociative();
            $indexedColumns = array_column($indexes, 'Column_name');
            
            foreach ($requiredColumns as $column) {
                if (!in_array($column, $indexedColumns)) {
                    echo "  ⚠️  Missing index on column: $column\n";
                    echo "     Suggested: ALTER TABLE $table ADD INDEX idx_{$column} ($column);\n";
                } else {
                    echo "  ✅ Index exists on column: $column\n";
                }
            }
        } catch (Exception $e) {
            echo "  ❌ Error checking table $table: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Check for slow queries in the slow query log
     */
    public function checkSlowQueries()
    {
        echo "\n=== Checking MySQL Configuration ===\n";
        
        try {
            // Check if slow query log is enabled
            $slowLogStatus = $this->connection->executeQuery("SHOW VARIABLES LIKE 'slow_query_log'")->fetchAssociative();
            echo "Slow Query Log: " . ($slowLogStatus['Value'] === 'ON' ? '✅ Enabled' : '⚠️  Disabled') . "\n";
            
            // Check slow query time threshold
            $slowLogTime = $this->connection->executeQuery("SHOW VARIABLES LIKE 'long_query_time'")->fetchAssociative();
            echo "Long Query Time: " . $slowLogTime['Value'] . " seconds\n";
            
            // Check max execution time
            $maxExecTime = ini_get('max_execution_time');
            echo "PHP Max Execution Time: " . ($maxExecTime ?: 'unlimited') . " seconds\n";
            
            if ($maxExecTime && $maxExecTime < 300) {
                echo "  ⚠️  Consider increasing max_execution_time for large data processing\n";
            }
            
        } catch (Exception $e) {
            echo "❌ Error checking MySQL configuration: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Test problematic queries with EXPLAIN
     */
    public function explainProblematicQueries()
    {
        echo "\n=== Analyzing Problematic Queries ===\n";
        
        $queries = [
            'Customer getLaterThan' => "SELECT c.* FROM cms2_customers c WHERE c.dtc > '2024-01-01' ORDER BY c.dtc ASC LIMIT 5000",
            'Company getLaterThan' => "SELECT co.* FROM cms2_company co WHERE co.dtc > '2024-01-01' ORDER BY co.dtc ASC LIMIT 5000",
            'Customers with ID check' => "SELECT c.* FROM cms2_customers c WHERE c.isIdCheckRequired = 1 ORDER BY c.customerId ASC LIMIT 1000"
        ];
        
        foreach ($queries as $name => $query) {
            echo "\n--- $name ---\n";
            try {
                $explain = $this->connection->executeQuery("EXPLAIN $query")->fetchAllAssociative();
                foreach ($explain as $row) {
                    $type = $row['type'] ?? 'unknown';
                    $rows = $row['rows'] ?? 'unknown';
                    $key = $row['key'] ?? 'none';
                    
                    echo "  Type: $type, Rows: $rows, Key: $key\n";
                    
                    if ($type === 'ALL' && $rows > 1000) {
                        echo "  ⚠️  Full table scan detected! Consider adding an index.\n";
                    }
                    if ($key === 'none' || empty($key)) {
                        echo "  ⚠️  No index used! This query may be slow.\n";
                    }
                }
            } catch (Exception $e) {
                echo "  ❌ Error explaining query: " . $e->getMessage() . "\n";
            }
        }
    }
    
    /**
     * Check table sizes
     */
    public function checkTableSizes()
    {
        echo "\n=== Table Sizes ===\n";
        
        try {
            $query = "
                SELECT 
                    table_name,
                    table_rows,
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                AND table_name LIKE 'cms2_%'
                ORDER BY (data_length + index_length) DESC
                LIMIT 10
            ";
            
            $tables = $this->connection->executeQuery($query)->fetchAllAssociative();
            
            foreach ($tables as $table) {
                $name = $table['table_name'];
                $rows = number_format($table['table_rows']);
                $size = $table['size_mb'];
                
                echo "  $name: $rows rows, {$size}MB\n";
                
                if ($table['table_rows'] > 100000) {
                    echo "    ⚠️  Large table - ensure queries are optimized\n";
                }
            }
        } catch (Exception $e) {
            echo "❌ Error checking table sizes: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Generate performance recommendations
     */
    public function generateRecommendations()
    {
        echo "\n=== Performance Recommendations ===\n";
        
        echo "1. 🔍 Enable MySQL slow query log:\n";
        echo "   SET GLOBAL slow_query_log = 'ON';\n";
        echo "   SET GLOBAL long_query_time = 2;\n\n";
        
        echo "2. 📊 Monitor query performance:\n";
        echo "   - Use EXPLAIN on slow queries\n";
        echo "   - Add indexes for frequently queried columns\n";
        echo "   - Use LIMIT clauses on large result sets\n\n";
        
        echo "3. 🚀 Optimize repository methods:\n";
        echo "   - Use batch processing for large datasets\n";
        echo "   - Implement pagination for user-facing queries\n";
        echo "   - Use iterators for memory-efficient processing\n\n";
        
        echo "4. 🔧 PHP Configuration:\n";
        echo "   - Increase memory_limit for large data processing\n";
        echo "   - Set appropriate max_execution_time\n";
        echo "   - Enable OPcache for better performance\n\n";
    }
    
    public function runFullCheck()
    {
        echo "🔍 CMS Performance Monitor\n";
        echo "========================\n";
        
        $this->checkMissingIndexes();
        $this->checkSlowQueries();
        $this->explainProblematicQueries();
        $this->checkTableSizes();
        $this->generateRecommendations();
        
        echo "\n✅ Performance check completed!\n";
    }
}

// Run the performance monitor
try {
    $container = \Services\Registry::getContainer();
    $entityManager = $container->get('doctrine.default.entityManager');
    
    $monitor = new PerformanceMonitor($entityManager);
    $monitor->runFullCheck();
    
} catch (Exception $e) {
    echo "❌ Error running performance monitor: " . $e->getMessage() . "\n";
    echo "Make sure the application is properly configured and the database is accessible.\n";
}
