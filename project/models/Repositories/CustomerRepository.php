<?php

namespace Repositories;

use CustomerModule\Entities\Events\DeletedEvent;
use CustomerModule\Queries\CustomerLogQueryBuilder;
use DateTime;
use Doctrine;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Internal\Hydration\IterableResult;
use DoctrineModule\SelfClearingIterator;
use Entities\Customer;
use Repositories\BaseRepository_Abstract;
use OrmModule\Exceptions\EntityException;
use OrmModule\Iterators\DoctrineIterator;
use UserModule\Contracts\ICustomer;
use UserModule\Repositories\ICustomerRepository;
use CustomerModule\Queries\CustomerQueryBuilder;
use Entities\CustomerLog;
use Iterator;
use Utils\Date;

class CustomerRepository extends BaseRepository_Abstract implements ICustomerRepository
{

    /**
     * @param DateTime $dtc
     * @param int $limit Maximum number of records to return (default: 5000)
     * @return IterableResult
     */
    public function getLaterThan(DateTime $dtc, int $limit = 5000)
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('i')->from('Entities\Customer', 'i');
        $qb->where('i.dtc > ?1')->setParameter(1, $dtc, Types::DATETIME_MUTABLE);
        $qb->setMaxResults($limit);
        $qb->orderBy('i.dtc', 'ASC'); // Add ordering for consistent results
        $iterator = $qb->getQuery()->iterate();
        return $iterator;
    }

    /**
     * @param int $batchSize Number of records to process per batch (default: 1000)
     * @return SelfClearingIterator
     */
    public function getCustomersWithRequiredIdCheck(int $batchSize = 1000)
    {
        $qb = $this->createQueryBuilder('c')
            ->where('c.isIdCheckRequired = TRUE')
            ->orderBy('c.customerId', 'ASC'); // Add ordering for consistent batching
        return new SelfClearingIterator($qb, $batchSize);
    }

    /**
     * @param string $email
     * @return bool
     */
    public function isEmailTaken($email)
    {
        return (bool)$this->findOneBy(['email' => $email]);
    }

    public function optionalCustomerByEmail(string $email): ?ICustomer
    {
        return $this->findOneBy(['email' => $email]);
    }

    public function requiredCustomerByEmail(string $email): ICustomer
    {
        $customer = $this->optionalCustomerByEmail($email);

        if (!$customer) {
            throw EntityException::notFound($email);
        }

        return $customer;
    }

    public function save(ICustomer $customer)
    {
        $this->saveEntity($customer);
    }

    public function changeToDeleted(Customer $customer, string $actionBy): DeletedEvent
    {
        $event = new DeletedEvent($customer, $actionBy);
        $customer->markAsDeleted();
        $this->saveEntity($customer);
        $this->saveEntity($event);
        return $event;
    }

    /**
     * @return Iterator|Customer[]
     */
    public function getCustomersWithCashBackWithoutCashBackLog(): DoctrineIterator
    {
        $customerQb = (new CustomerQueryBuilder($this->createSimpleBuilder()))
            ->getCustomers()
            ->withCashBackTypeProvided()
            ->getQueryBuilder();

        return (new CustomerLogQueryBuilder($customerQb))
            ->withoutCustomerLog(CustomerLog::CASHBACK_PREF_CREATED)
            ->iterate();

    }

    /**
     * @string $cashBackType
     * @return Iterator|Customer[]
     */
    public function getCustomersWithEligibleCashBacks(string $cashBackType): Iterator
    {
        return (new CustomerQueryBuilder($this->createSimpleBuilder()))
            ->getCustomers()
            ->withEligibleCashBacksOfType($cashBackType)
            ->iterate();
    }

    /**
     * @TODO remove once all ID3 Check customers gets converted
     */
    public function getCustomersID3Global(int $limit = 100, array $customerIds = null)
    {
        $qb = $this->createSimpleBuilder()->setMaxResults($limit)->orderBy('c.dtc', 'DESC')->where('c.dtc <= :fromDate')->setParameter('fromDate', new Date('-2 day'));

        if ($customerIds) {
            $qb->andWhere('c.customerId IN (:customerIds)')->setParameter('customerIds', $customerIds);
        } else {
            $qb->andWhere('c.deleted = 0');
        }

        return (new CustomerQueryBuilder($qb))->getCustomers()->iterate();
    }


    public function getSagepayCustomersWithActiveToken(int $batchSize = 1000): Iterator
    {
        $qb = (new CustomerQueryBuilder($this->createSimpleBuilder()))
            ->getCustomers()
            ->withLastMonthActiveToken()
            ->getQueryBuilder()
            ->setMaxResults($batchSize)
            ->orderBy('c.customerId', 'ASC'); // Add ordering for consistent results

        return (new CustomerQueryBuilder($qb))->iterate();
    }
}