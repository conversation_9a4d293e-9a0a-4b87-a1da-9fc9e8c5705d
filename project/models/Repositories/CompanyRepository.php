<?php

namespace Repositories;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Internal\Hydration\IterableResult;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Entities\AnnualReturnService;
use Entities\Company;
use Entities\Company as CompanyEntity;
use Entities\CompanyHouse\FormSubmission;
use Entities\Customer;
use Entities\Customer as CustomerEntity;
use Entities\Event;
use Entities\OrderItem;
use Entities\Service;
use Entities\ServiceSettings;
use Models\OldModels\Customer as OldCustomer;
use Search\CompanySearchQuery;
use Search\ICompanyRepository;
use ServiceModule\Commands\AutoRenewalEnablerCommand;
use UserModule\Contracts\ICustomer;
use UserModule\Repositories\ICompanyFormation;
use Utils\Date;

class CompanyRepository extends BaseRepository_Abstract implements ICompanyRepository, ICompanyFormation
{
    /**
     * @param \DateTime $dtc
     * @param int $limit Maximum number of records to return (default: 5000)
     *
     * @return IterableResult
     */
    public function getLaterThan(\DateTime $dtc, int $limit = 5000)
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('i')->from('Entities\Company', 'i');
        $qb->where('i.dtc > ?1')->setParameter(1, $dtc, Types::DATETIME_MUTABLE);
        $qb->setMaxResults($limit);
        $qb->orderBy('i.dtc', 'ASC'); // Add ordering for consistent results

        return $qb->getQuery()->iterate();
    }

    public function getCustomerCompanyCount(Customer $customer): int
    {
        return $this->createQueryBuilder('co')
            ->select('count(co)')
            ->where('co.customer = :customer')
            ->setParameter('customer', $customer)
            ->getQuery()->getSingleScalarResult();
    }

    /**
     * @param CustomerEntity $customer
     *
     * @return IterableResult
     */
    public function getCustomerServiceCompanies(CustomerEntity $customer)
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('c')->from('Entities\Company', 'c');
        $qb->join('Entities\Service', 's', 'WITH', 's.company=c.companyId');
        $qb->where('c.customer = ?1')->setParameter(1, $customer);
        $qb->andWhere('c.companyNumber IS NOT NULL');
        $qb->andWhere('c.deleted != 1');
        $qb->andWhere('c.companyNumber NOT LIKE ?2')->setParameter(2, 'DELETED_%');
        $qb->andWhere('c.hidden != 1');
        $qb->having('COUNT(s.serviceId) > 0');
        $qb->groupBy('s.company');

        return $qb->getQuery()->getResult();
    }

    /**
     * @param CustomerEntity $customer
     *
     * @return CompanyEntity[]
     */
    public function getUnsubmittedCompanies(CustomerEntity $customer)
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('c')->from('Entities\Company', 'c');
        $qb->innerJoin('c.formSubmissions', 'f');
        $qb->where('f INSTANCE OF :identifier');
        $qb->andWhere('f.response IS NULL ');
        $qb->andWhere('c.customer = :customer');
        $qb->setParameter('identifier', FormSubmission::TYPE_COMPANY_INCORPORATION);
        $qb->setParameter('customer', $customer);
        $qb->orderBy('c.companyId', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * @return Company[]
     */
    public function getCompaniesWithActiveServices()
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('c')->from('Entities\Company', 'c');
        $qb->innerJoin('c.services', 's');
        $qb->where('s.dtExpires >= ?1');
        $qb->andWhere('s.dtExpires <= ?2');
        $qb->setParameter('1', new \DateTime('-29 days 00:00:00'));
        $qb->setParameter('2', new \DateTime('29 days 23:59:59'));

        return $qb->getQuery()->getResult();
    }

    public function getCompaniesWithActiveUltimateService()
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('c')->from('Entities\Company', 'c');
        $qb->innerJoin('c.services', 's');
        $qb->where('s.dtStart <= :today');
        $qb->andWhere('s.dtExpires >= :today');
        $qb->andWhere('s.serviceTypeId IN (:packages)');
        $qb->setParameter('today', new \DateTime());
        $qb->setParameter('packages', [Service::TYPE_PACKAGE_ULTIMATE]);

        return $qb->getQuery()->getResult();
    }

    /**
     * @param Customer $customer
     * @param bool     $excludeDeleted
     *
     * @return array|Company[]
     */
    public function getCustomerIncorporatedCompanies(Customer $customer, $excludeDeleted = false)
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('c')->from('Entities\Company', 'c');
        $qb->where('c.customer = :customer');
        $qb->andWhere('c.companyNumber IS NOT NULL AND c.companyNumber != :companyNumber');
        $qb->setParameter('customer', $customer);
        $qb->setParameter('companyNumber', '');
        if ($excludeDeleted) {
            $qb->andWhere('c.deleted = FALSE');
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * @param CompanyEntity $company
     *
     * @return FormSubmission|FormSubmission\CompanyIncorporation|null
     */
    public function getLastCompanyIncorporationSubmission(CompanyEntity $company)
    {
        $qb = $this->_em->createQueryBuilder()
            ->select('f')
            ->from('Entities\CompanyHouse\FormSubmission', 'f')
            ->where('f.company = :company')->setParameter('company', $company)
            ->andWhere('f INSTANCE OF :type')->setParameter('type', FormSubmission::TYPE_COMPANY_INCORPORATION)
            ->orderBy('f.dtm', 'DESC')
            ->setMaxResults(1);

        return $qb->getQuery()->getOneOrNullResult();
    }

    public function filterResultsByCustomerId(int $customerId, CompanySearchQuery $companySearchQuery)
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('c')->from('Entities\Company', 'c');
        $qb->where('c.companyNumber IS NOT NULL AND c.companyNumber != :companyNumber');
        $qb->setParameter('companyNumber', '');
        $qb->andWhere('c.customer = :customer');
        $qb->setParameter('customer', $customerId);
        $qb->andWhere('c.deleted = 0');
        $qb->andWhere('c.hidden = 0');
        if ($companySearchQuery->getTerm()) {
            $qb->andWhere('c.companyName LIKE :term OR c.companyNumber LIKE :term');
            $qb->setParameter('term', '%' . $companySearchQuery->getTerm() . '%');
        }
        if ($companySearchQuery->isOnlyActive()) {
            $qb->andWhere('c.companyStatus != :status');
            $qb->setParameter('status', Company::COMPANY_STATUS_DISSOLVED);
        }
        if ($companySearchQuery->isAccounts()) {
            if ($companySearchQuery->getDateFrom()) {
                $qb->andWhere('c.accountsNextDueDate >= :fromDate');
                $qb->setParameter('fromDate', $companySearchQuery->getDateFrom());
            }
            if ($companySearchQuery->getDateTo()) {
                $qb->andWhere('c.accountsNextDueDate <= :toDate');
                $qb->setParameter('toDate', $companySearchQuery->getDateTo());
            }
        }
        if ($companySearchQuery->isReturns()) {
            if ($companySearchQuery->getDateFrom()) {
                $qb->andWhere('c.returnsNextDueDate >= :fromDate');
                $qb->setParameter('fromDate', $companySearchQuery->getDateFrom());
            }
            if ($companySearchQuery->getDateTo()) {
                $qb->andWhere('c.returnsNextDueDate <= :toDate');
                $qb->setParameter('toDate', $companySearchQuery->getDateTo());
            }
        }
        if ($companySearchQuery->getLimit() && $companySearchQuery->getLimit() != 0) {
            $qb->setMaxResults($companySearchQuery->getLimit());
        }
        if ($companySearchQuery->getOffset() > 0) {
            $qb->setFirstResult($companySearchQuery->getOffset());
        }
        if ($companySearchQuery->getSortingDirection() && $companySearchQuery->getSortingField()) {
            $qb->orderBy('c.' . $companySearchQuery->getSortingField(), $companySearchQuery->getSortingDirection());
        }

        return $qb;
    }

    /**
     * @param CustomerEntity     $customer
     * @param CompanySearchQuery $companySearchQuery
     *
     * @return QueryBuilder
     */
    public function filterResults(CustomerEntity $customer, CompanySearchQuery $companySearchQuery)
    {
        return $this->filterResultsByCustomerId($customer->getId(), $companySearchQuery);
    }

    /**
     * @param CustomerEntity     $customer
     * @param CompanySearchQuery $companySearchQuery
     *
     * @return IterableResult
     */
    public function findByQuery(CustomerEntity $customer, CompanySearchQuery $companySearchQuery)
    {
        $qb = $this->filterResults($customer, $companySearchQuery);

        return $qb->getQuery()->iterate();
    }

    /**
     * @param CustomerEntity     $customer
     * @param CompanySearchQuery $companySearchQuery
     *
     * @return int
     */
    public function countByQuery(CustomerEntity $customer, CompanySearchQuery $companySearchQuery)
    {
        $qb = $this->filterResults($customer, $companySearchQuery);
        $qb->select('count(c)');

        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param CompanyEntity $company
     */
    public function refresh(CompanyEntity $company)
    {
        $this->_em->refresh($company);
    }

    /**
     * @param Customer $customer
     * @param int      $companyId
     *
     * @throws NoResultException
     *
     * @return Company
     */
    public function getCustomerCompany(CustomerEntity $customer, $companyId)
    {
        $company = $this->findOneBy(['customer' => $customer, 'companyId' => $companyId]);
        if (!$company) {
            throw new NoResultException();
        }

        return $company;
    }

    public function isFormingCompaniesProfessionally(ICustomer $customer): bool
    {
        $qb = $this->createQueryBuilder('c');
        $qb->select('count(c)')
            ->where('c.customer = :customer')
            ->andWhere('c.companyNumber IS NOT NULL AND c.companyNumber != :companyNumber')
            ->andWhere('c.order IS NOT NULL')
            ->andWhere('c.dtc >= :fromDate')
            ->setParameter('customer', $customer)
            ->setParameter('companyNumber', '')
            ->setParameter('fromDate', new Date('-1 year'));

        return $qb->getQuery()->getSingleScalarResult() > self::PROFESSIONAL_AFTER_FORMED;
    }

    /**
     * @return Company[]
     */
    public function getLastFiveIncorporatedCompanies(Customer $customer): array
    {
        $qb = $this->_em->createQueryBuilder();

        $qb->select('c')->from(Company::class, 'c')
            ->where('c.customer = :customer')
            ->andWhere('c.companyNumber IS NOT NULL AND c.companyNumber != :companyNumber')
            ->andWhere('c.productId IS NOT NULL')
            ->setParameter('customer', $customer)
            ->setParameter('companyNumber', '')
            ->andWhere('c.deleted = FALSE')
            ->setMaxResults(5)
            ->orderBy('c.incorporationDate', 'DESC');

        return $qb->getQuery()->getResult();
    }

    public function getCustomerCompaniesListBuilder(CustomerEntity $customer): QueryBuilder
    {
        $subQuery = $this->createSimpleBuilder()
            ->select('MAX(fs2.formSubmissionId)')
            ->from(FormSubmission::class, 'fs2')
            ->where('fs2.company = c.companyId')
            ->andWhere('fs2 INSTANCE OF Entities\CompanyHouse\FormSubmission\CompanyIncorporation');

        return $this->createSimpleBuilder()
            ->select('c.companyId, c.companyName, c.companyNumber, c.incorporationDate, c.companyStatus, fs.response as formStatus')
            ->from(Company::class, 'c')
            ->leftJoin(FormSubmission::class, 'fs', Join::WITH, 'fs.formSubmissionId = (' . $subQuery->getDQL() . ')')
            ->where('c.customer = :customer')
            ->andWhere('c.deleted = FALSE')
            ->setParameter('customer', $customer->getId())
            ->orderBy('c.companyId', 'DESC');
    }

    public function getCompaniesMissingAuthCodeWithCS(string $eventKey, ?string $secondEventKey = null): IterableResult
    {
        $qb = $this->createSimpleBuilder()
            ->select('c.companyId, c.annualReturnId, a.annualReturnServiceId, max(e.dtc) lastEventDate')
            ->from(Company::class, 'c')
            ->leftJoin(Event::class, 'e', Join::WITH, '(e.objectId = c.companyId AND e.eventKey = :eventKey)')
            ->join(AnnualReturnService::class, 'a', Join::WITH, 'a.company = c.companyId')
            ->where('c.authenticationCode IS NULL')
            ->andWhere('c.annualReturnId IS NOT NULL')
            ->setParameter('eventKey', $eventKey)
            ->orderBy('c.annualReturnId', 'ASC')
            ->groupBy('c.companyId');

        if ($secondEventKey) {
            $qb->leftJoin(Event::class, 'e2', Join::WITH, '(e2.objectId = c.companyId AND e2.eventKey = :secondEventKey)')
                ->setParameter('secondEventKey', $secondEventKey)
                ->having('count(e.eventId) >= 4 and count(e2.eventId) < 1');
        } else {
            $qb->having('count(e.eventId) < 4');
        }

        return $qb->getQuery()->iterate();
    }

    public function getCompaniesPendingOnApprovalCS(string $eventKey, ?string $secondEventKey = null): IterableResult
    {
        $qb = $this->createSimpleBuilder()
            ->select('c.companyId, c.annualReturnId, a.annualReturnServiceId, max(e.dtc) lastEventDate')
            ->from(Company::class, 'c')
            ->leftJoin(Event::class, 'e', Join::WITH, '(e.objectId = c.companyId AND e.eventKey = :eventKey)')
            ->join(AnnualReturnService::class, 'a', Join::WITH, 'a.company = c.companyId')
            ->where('c.authenticationCode IS NOT NULL')
            ->andWhere('a.statusId = :statusId')
            ->andWhere('a.dtm < :dateAnnualReturnService')
            ->setParameter('eventKey', $eventKey)
            ->setParameter('statusId', AnnualReturnService::STATUS_COMPLETED)
            ->setParameter('dateAnnualReturnService', new \DateTime('- 7 days'))
            ->orderBy('c.companyId', 'ASC')
            ->groupBy('c.companyId');

        if ($secondEventKey) {
            $qb->leftJoin(Event::class, 'e2', Join::WITH, '(e2.objectId = c.companyId AND e2.eventKey = :secondEventKey)')
                ->setParameter('secondEventKey', $secondEventKey)
                ->having('count(e.eventId) >= 4 and count(e2.eventId) < 1');
        } else {
            $qb->having('count(e.eventId) < 4');
        }

        return $qb->getQuery()->iterate();
    }

    public function getCompaniesAlmostDueToCSWithoutAnnualReturn(string $eventKey, string $amountOfDays): IterableResult
    {
        return $this->createSimpleBuilder()
            ->select('c')
            ->from(Company::class, 'c')
            ->leftJoin(Event::class, 'e', Join::WITH, '(e.objectId = c.companyId AND e.eventKey = :eventKey)')
            ->where('c.authenticationCode IS NOT NULL')
            ->andWhere('c.annualReturnId IS NULL')
            ->andWhere('e.eventId IS NULL')
            ->andWhere('c.returnsNextDueDate = :returnsNextDueDate')
            ->setParameter('returnsNextDueDate', new Date(sprintf('%s days', $amountOfDays)))
            ->setParameter('eventKey', $eventKey)
            ->getQuery()->iterate();
    }

    public function getCompaniesCSDIYNotCompleted(string $eventKey, string $amountOfDays): IterableResult
    {
        return $this->createSimpleBuilder()
            ->select('c')
            ->from(Company::class, 'c')
            ->leftJoin(Event::class, 'e', Join::WITH, '(e.objectId = c.companyId AND e.eventKey = :eventKey)')
            ->where('c.authenticationCode IS NOT NULL')
            ->andWhere('c.annualReturnId = 666')
            ->andWhere('c.returnsNextDueDate = :returnsNextDueDate')
            ->andWhere('e.eventId IS NULL')
            ->setParameter('returnsNextDueDate', new Date(sprintf('%s days', $amountOfDays)))
            ->setParameter('eventKey', $eventKey)
            ->orderBy('c.companyId', 'ASC')
            ->groupBy('c.companyId')
            ->getQuery()->iterate();
    }

    public function getCompaniesWithFirstEventWithoutSecond(string $firstEvent, string $secondEvent): IterableResult
    {
        return $this->createSimpleBuilder()
            ->select('c.companyId, c.annualReturnId, a.annualReturnServiceId, max(e.dtc) lastEventDate')
            ->from(Company::class, 'c')
            ->join(Event::class, 'e', Join::WITH, '(e.objectId = c.companyId AND e.eventKey = :eventKey)')
            ->leftJoin(Event::class, 'e', Join::WITH, '(e.objectId = c.companyId AND e.eventKey = :eventKey)')
            ->setParameter('firstEvent', $firstEvent)
            ->setParameter('secondEvent', $secondEvent)
            ->orderBy('c.companyId', 'ASC')
            ->having('count(e.eventId) > 0 and count(e2.eventId) = 0')
            ->getQuery()->iterate();
    }

    public function getCompaniesWithDeletedCustomer(): IterableResult
    {
        return $this->_em->createQueryBuilder()
            ->select('c')->from(Company::class, 'c')
            ->join(Customer::class, 'cu', 'WITH', 'c.customer = cu')
            ->where('c.deleted = 0')
            ->andWhere('cu.deleted = 1')
            ->getQuery()->iterate();
    }

    // TODO: REMOVE?
    public function getImportedCompaniesNotTakenInTheLast30Days(): array
    {
        return [];
    }

    public function getIncorporatedCompanyIds(): iterable
    {
        $qb = $this->createQueryBuilder();
        $qb->select('co.companyId')
            ->from(Company::class, 'co')
            ->where('co.deleted != true')
            ->orderBy('co.companyId', 'ASC');

        return $qb->getQuery()->iterate();
    }

    /**
     * This query is highly specific for the AutoRenewalEnablerCommand.
     */
    public function getEligibleCompaniesToEnableAutoRenewal()
    {
        $daysBeforeSendingFirstManualRenewalReminder = sprintf(
            '%s days',
            AutoRenewalEnablerCommand::DAYS_BEFORE_SENDING_FIRST_RENEWAL_REMINDER
        );

        return $this
            ->createQueryBuilder('co')
            ->innerJoin(
                Service::class,
                's',
                Join::WITH,
                's.company = co.companyId'
            )
            ->innerJoin(
                Customer::class,
                'c',
                Join::WITH,
                'c.customerId = co.customer'
            )
            ->innerJoin(
                OrderItem::class,
                'oi',
                Join::WITH,
                'oi.order = s.order'
            )
            ->innerJoin(
                ServiceSettings::class,
                'ss',
                Join::WITH,
                'ss.company = s.company AND ss.serviceTypeId = s.serviceTypeId'
            )
            ->andWhere('s.stateId = :enabled')
            ->andWhere('s.parent IS NULL')
            ->andWhere('s.renewalProductId IS NOT NULL')
            ->andWhere('s.dtStart <= CURRENT_DATE()')
            ->andWhere('s.dtExpires > :expires')
            ->andWhere('s.serviceTypeId in (:addressRelatedTypeServices)')
            ->andWhere('ss.isAutoRenewalEnabled = 0')
            ->andWhere('co.companyStatus IN (:statuses)')
            ->andWhere('co.companyNumber IS NOT NULL')
            ->andWhere('c.roleId = :roleNormal')
            ->andWhere('c.deleted = 0 OR c.deleted IS NULL')
            ->andWhere('co.deleted = 0 OR co.deleted IS NULL')
            ->andWhere('co.locked = 0  OR co.locked IS NULL')
            ->andWhere('co.hidden = 0 OR co.hidden IS NULL')
            ->andWhere('oi.isRefunded = 0 OR oi.isRefunded IS NULL')
            ->andWhere('oi.isRefund = 0 OR oi.isRefund IS NULL')
            ->setParameter('enabled', Service::STATE_ENABLED)
            ->setParameter('expires', new Date($daysBeforeSendingFirstManualRenewalReminder))
            ->setParameter('addressRelatedTypeServices', Service::ADDRESS_RELATED_SERVICE_TYPES)
            ->setParameter('statuses', [Company::COMPANY_STATUS_ACTIVE, Company::COMPANY_STATUS_ACTIVE_STRIKE_OFF])
            ->setParameter('roleNormal', OldCustomer::ROLE_NORMAL)
            ->getQuery()
            ->getResult();
    }
}
