<?php

namespace FrontModule\controlers;

use BasketModule\Services\BasketService;
use CompaniesHouseModule\Services\SubmissionReviewService;
use Doctrine\ORM\NonUniqueResultException;
use Entities\Service;
use Exception;
use Exceptions\Technical\NodeException;
use Framework\FApplication;
use IdModule\Repositories\IdInfoCustomerRepository;
use Libs\CHFiling\Core\CHFiling;
use Libs\Exceptions\EntityNotFound;
use MailScanModule\Deciders\ForwardingAddressDecider;
use MailScanModule\Exceptions\InvalidForwardingAddressException;
use MailScanModule\Exceptions\NotAMailboxProductException;
use Models\OldModels\Customer;
use NotificationModule\Services\NotificationService;
use Repositories\CompanyHouse\FormSubmissionRepository;
use Repositories\CompanyRepository;
use RouterModule\Generators\IUrlGenerator;
use Entities\CompanyHouse\FormSubmission;
use Entities\Company as CompanyEntity;
use Services\CustomerService as CustomerService;
use ServiceSettingsModule\Services\ServiceSettingsService;
use DateTime;
use Utils\Helpers\ArrayHelper;

class DashboardCustomerControler extends LoggedControler
{
    
    // pages
    const MYACCOUNT_PAGE = 65;
    const DASHBOARD_PAGE = 93;
    const BUSINESS_LIBRARY_PAGE = 236;
    const STATUTORY_FORMS_PAGE = 676;

    /**
     * @var NotificationService
     */
    private $notificationService;

    /**
     * @var IdInfoCustomerRepository
     */
    private $idInfoCustomerRepository;

    /**
     * @var SubmissionReviewService
     */
    private $submissionReviewService;

    /**
     * @var CompanyRepository
     */
    private $companyRepository;

    /**
     * @var IUrlGenerator
     */
    private $urlGenerator;

    /**
     * @var CustomerService
     */
    protected $customerService;

    /**
     * @var BasketService
     */
    private $basketService;

    /**
     * @var ServiceSettingsService
     */
    private $serviceSettingsService;

    /**
     * @var ForwardingAddressDecider
     */
    protected ForwardingAddressDecider $forwardingAddressDecider;

    /**
     * @var FormSubmissionRepository
     */
    private $formSubmissionRepository;

    public function startup()
    {
        $this->finishedRegistrationRequired = true;
        parent::startup();

        $this->notificationService = $this->getService('notification_module.notification_service');
        $this->idInfoCustomerRepository = $this->getService('id_module.repositories.id_info_customer_repository');
        $this->submissionReviewService = $this->getService('companies_house_module.services.submission_review_service');
        $this->companyRepository = $this->getService('repositories.company_repository');
        $this->urlGenerator = $this->getService('url_generator');
        $this->customerService = $this->getService('services.customer_service');
        $this->basketService = $this->getService('basket_module.services.basket_service');
        $this->serviceSettingsService = $this->getService('service_settings_module.services.service_settings_service');
        $this->forwardingAddressDecider = $this->getService('mail_scan_module.deciders.forwarding_address_decider');
        $this->formSubmissionRepository = $this->getService('repositories.company_house.form_submission_repository');
    }

    public function beforePrepare()
    {
        if ($this->nodeId == self::MYACCOUNT_PAGE) {
            $this->redirect(self::DASHBOARD_PAGE);
        }

        parent::beforePrepare();
    }

    public function renderDefault()
    {
        $this->template->actionPanels = $this->getActionPanels();
        $this->template->seo = ['title' => 'Dashboard'];
        $this->template->customerEntity = $this->customerEntity;
    }

    private function getIdPanel(): ?array
    {
        $companyCount = $this->companyRepository->getCustomerCompanyCount($this->customerEntity);

        // @TODO getNotValidCompanies bug timeout with many companies
        if ($companyCount > 100) {
            return null;
        }

        $companies = [];
        $idCustomerInfo = $this->idInfoCustomerRepository->getIdInfoForCustomer($this->customerEntity, true);

        foreach ($idCustomerInfo->getNotValidCompanies() as $idCompanyInfo) {
            $company = $idCompanyInfo->getCompany();
            $companies[] = [
                'companyName' => $company->getName(),
                'link' => $this->urlGenerator->url('id_entity_checks', ['company' => $company->getId()]),
                'cta' => 'Manage',
                'status' => 'INCOMPLETE'
            ];
        }

        $companiesCount = count($companies);
        if ($companiesCount === 0) {
            return [];
        }

        return [
            'key' => 'id-check',
            'title' => 'ID Check',
            'companies' => $companies,
            'count' => $companiesCount
        ];
    }

    /**
     * @throws NotAMailboxProductException
     * @throws InvalidForwardingAddressException
     * @throws EntityNotFound
     * @throws NonUniqueResultException
     * @throws NodeException
     */
    private function getActionPanels(): array
    {
        $panels = [];

        $missingForwardingAddressCompanies = $this->getMissingForwardingAddressCompanies();

        if (!empty($missingForwardingAddressCompanies)) {
            $panels[] = [
                'key' => 'missing-forwarding-address',
                'title' => 'Missing forwarding address',
                'companies' => $missingForwardingAddressCompanies,
                'count' => count($missingForwardingAddressCompanies)
             ];
        }

        ini_set('memory_limit', '512M');
        $lastSubmissions = $this->getLastSubmissions($this->customer);

        if ($idPanel = $this->getIdPanel()) {
            $panels[] = $idPanel;
        }

        foreach ($lastSubmissions as $type => $submissions) {
            $panels[] = [
                'key' => str_replace(' ', '-', strtolower($this->getSubmissionType($type))),
                'title' => $this->getSubmissionType($type),
                'companies' => $submissions,
                'count' => count($submissions)
            ];
        }

        $dueAndOverduePackages = $this->getDueAndOverduePackages();

        if (!empty($dueAndOverduePackages)) {
            $panels[] = [
                'key' => 'due-packages',
                'title' => 'Package Subscription',
                'companies' => $dueAndOverduePackages,
                'count' => count($dueAndOverduePackages)
            ];
        }

        return $panels;
    }

    public function getLastSubmissions(Customer $customer): array
    {
        $return = [];

        $submissions = CHFiling::getIncompleteFormSubmissions($customer->getId());
        foreach ($submissions as $submission) {

            if ($submission['deleted']) {
                continue;
            }

            $type = $submission['form_identifier'];

            // on error no action
            if ($submission['response'] === 'ERROR') {
                $return[$type][] = [
                    'companyName' => $submission['company_name'],
                    'link' => null,
                    'cta' => null,
                    'status' => $submission['response']
                ];
                continue;
            }
            $previousResponse = [];
            if ($type === FormSubmission::TYPE_COMPANY_INCORPORATION) {
                if ($this->submissionReviewService->hasRegistrationReview($submission['form_submission_id'])) {
                    continue;
                }

                if ($submission['response'] === FormSubmission::RESPONSE_INCOMPLETE) {
                    $previousResponse = $this->formSubmissionRepository->getPreviousCompanyIncorporationResponse($submission['company_id'], $submission['form_submission_id']);
                }
            }

            $return[$type][] = [
                'companyName' => $submission['company_name'],
                'link' => $this->getSubmissionLink($type, $submission['company_id']),
                'cta' => 'Continue',
                'status' => ArrayHelper::get($previousResponse, 'response', null) === FormSubmission::RESPONSE_REJECT ? FormSubmission::RESPONSE_REJECTED : $submission['response']
            ];
        }

        return $return;
    }

    private function getSubmissionLink(string $type, string $companyId): ?string
    {
        if ($type === FormSubmission::TYPE_COMPANY_INCORPORATION) {
            return $this->urlGenerator->url(
                'company_formation_module.formation',
                ['company' => $companyId]
            );
        }

        if ($type === FormSubmission::TYPE_ANNUAL_RETURN) {
            return FApplication::$router->link(
                AnnualReturnControler::COMPANY_DETAILS_PAGE,
                "company_id={$companyId}"
            );
        }

        if ($type === FormSubmission::TYPE_CHANGE_OF_NAME) {
            return FApplication::$router->link(
                CUChangeNameControler::CHANGE_NAME_PAGE,
                "company_id={$companyId}"
            );
        }

        return null;
    }

    private function getSubmissionType(string $type): string
    {
        if ($type === FormSubmission::TYPE_COMPANY_INCORPORATION) {
            return 'Company Incorporation';
        }

        if ($type === FormSubmission::TYPE_ANNUAL_RETURN) {
            return 'Confirmation Statement';
        }

        if ($type === FormSubmission::TYPE_CHANGE_OF_NAME) {
            return 'Change of name';
        }

        return 'Other';
    }

    private function getDueAndOverduePackages(): array
    {
        try {
            $customer = $this->customerService->getCustomerById($this->customer->getId());
            $companies = $this->companyRepository->getCustomerServiceCompanies($customer);

            $dueAndOverduePackages = [];

            /** @var CompanyEntity $company */
            foreach ($companies as $company) {
                $services = $company->getServices();

                $packages = $services->filter(function (Service $service) {
                    try {
                        return $service->isCorePackageType() && !$service->isMonthly();
                    } catch (Exception $e) {
                        return false;
                    }
                });

                $packagesArray = $packages->toArray();

                usort($packagesArray, function (Service $serviceA, Service $serviceB) {
                    return $serviceB->getDtExpires() > $serviceA->getDtExpires() ? 1 : -1;
                });

                /** @var Service $package */
                foreach ($packagesArray as $package) {
                    $isDue = $this->isPackageDue($package);

                    if ($package->isActive() && !$isDue && !$package->isOverdue()) {
                        break;
                    }

                    if ($package->isOnHold()) {
                        break;
                    }

                    if ($settings = $this->serviceSettingsService->getSettingsByService($package)) {
                        if ($settings->isServiceCancelled()) {
                            break;
                        }
                    }

                    if ($isDue && !$settings?->isAutoRenewalEnabled()) {
                        $dueAndOverduePackages[] = [
                            'companyName' => $company->getName(),
                            'link' => $this->getBasketRenewalLink($customer->getId(), $package),
                            'cta' => 'Renew',
                            'status' => 'DUE'
                        ];
                        break;
                    }

                    if ($package->isOverdue()) {
                        $dueAndOverduePackages[] = [
                            'companyName' => $company->getName(),
                            'link' => $this->getBasketRenewalLink($customer->getId(), $package),
                            'cta' => 'Renew',
                            'status' => 'OVERDUE'
                        ];
                        break;
                    }
                }
            }
        } catch (Exception $e) {
            return [];
        }

        return $dueAndOverduePackages;
    }

    private function isPackageDue(Service $service): bool
    {
        if (!$service->isCorePackageType()) {
            return false;
        }

        $now = new DateTime();
        $dueEnd = new DateTime('+28 days');
        $dueEnd->setTime(23, 59, 59);
        $dtExpires = $service->getDtExpires();

        return $service->isActive() && $dtExpires >= $now && $dtExpires <= $dueEnd;
    }

    private function getBasketRenewalLink(int $customerId, Service $service): string
    {
        return $this->controllerHelper->url(
            'basket_add_service',
            [
                'customer'  => $customerId,
                'serviceId' => $service->getId(),
                'productId' => $service->getProductId()
            ]
        );
    }

    /**
     * @throws NotAMailboxProductException
     * @throws EntityNotFound
     * @throws InvalidForwardingAddressException
     * @throws NonUniqueResultException
     * @throws NodeException
     */
    private function getMissingForwardingAddressCompanies(): array
    {
        try {
            $customer = $this->customerService->getCustomerById($this->customer->getId());
            $companies = $this->companyRepository->getCustomerServiceCompanies($customer);

            $missingForwardingAddressCompanies = [];

            /** @var CompanyEntity $company */
            foreach ($companies as $company) {
                try {
                    if ($this->forwardingAddressDecider->companyNeedsMailForwardingAddress($company)) {
                        $missingForwardingAddressCompanies[] = [
                            'companyName' => $company->getName(),
                            'link' => $this->urlGenerator->url(
                                'mail_scan_module.inbox_settings',
                                ['companyId' => $company->getId()]
                            ),
                            'cta' => 'Manage',
                            'status' => 'INCOMPLETE'
                        ];
                    }
                } catch (\Throwable $e) {
                    $this->logger->critical($e->getMessage());
                }
            }
            return $missingForwardingAddressCompanies;
        } catch (Exception $e) {
            $this->logger->error($e->getMessage(), ['exception' => $e]);
            return [];
        }
    }
}
