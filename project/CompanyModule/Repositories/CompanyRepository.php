<?php

namespace CompanyModule\Repositories;

use CompanyModule\Queries\CompanyQueryBuilder;
use Doctrine\ORM\EntityNotFoundException;
use Doctrine\ORM\QueryBuilder;
use EmailModule\IEmail;
use Entities\Company;
use Entities\Customer;
use Models\Products\Package;
use OrmModule\Repositories\IQueryableRepository;
use Utils\Date;

class CompanyRepository
{
    public function __construct(
        private readonly IQueryableRepository $repository,
        private readonly array $corePackages,
    ) {}

    public function getDeletedCompanies(int $batchSize = 1000): iterable
    {
        $qb = (new CompanyQueryBuilder($this->repository->createSimpleBuilder()))
            ->getCompanies()
            ->deleted()
            ->getQueryBuilder()
            ->setMaxResults($batchSize)
            ->orderBy('co.companyId', 'ASC'); // Add ordering for consistent batching

        return (new CompanyQueryBuilder($qb))->iterate();
    }

    /**
     * @return \Iterator|Company[]
     */
    public function getCompaniesForEducationEmails(Date $intervalStart, Date $intervalEnd, int $batchSize = 1000): iterable
    {
        $qb = (new CompanyQueryBuilder($this->repository->createSimpleBuilder()))
            ->getCompanies()
            ->withRetailCustomer()
            ->notImported()
            ->withIncorporationDateWithinRange($intervalStart, $intervalEnd)
            ->getQueryBuilder()
            ->setMaxResults($batchSize)
            ->orderBy('co.incorporationDate', 'ASC'); // Add ordering for consistent results

        return (new CompanyQueryBuilder($qb))->iterate();
    }

    /**
     * @return \Iterator|Company[]
     */
    public function getCompaniesForEmailId(int $emailId): iterable
    {
        $intervalStart = new Date('-8 days');
        $intervalEnd = new Date('-3 days');

        return (new CompanyQueryBuilder($this->repository->createSimpleBuilder()))
            ->getCompanies()
            ->notImported()
            ->withProductIdIn($this->corePackages)
            ->withRetailCustomer()
            ->withoutPSC()
            ->withIncorporationDateWithinRange($intervalStart, $intervalEnd)
            ->isFirstEmail($emailId)
            ->iterate();
    }

    public function optionalById(int $companyId): ?Company
    {
        /* @phpstan-ignore-next-line */
        return $this->repository->optionalOneBy(['companyId' => $companyId]);
    }

    /**
     * @throws EntityNotFoundException
     */
    public function requiredById(int $companyId): Company
    {
        $company = $this->optionalById($companyId);

        if (!$company) {
            throw new EntityNotFoundException();
        }

        return $company;
    }

    /**
     * @param IEmail $email
     *
     * @return \Iterator|Company[]
     */
    public function getCompaniesForFreeDaysOffer(IEmail $email): iterable
    {
        $emailId = $email->getNodeId();
        $intervalStart = new Date('-30 days');
        $intervalEnd = new Date('-14 days');

        return (new CompanyQueryBuilder($this->repository->createSimpleBuilder()))
            ->getCompanies()
            ->withUkCustomer()
            ->notImported()
            ->withProductIdIn([Package::PACKAGE_BASIC, Package::PACKAGE_PRIVACY, Package::PACKAGE_COMPREHENSIVE])
            ->withIncorporationDateWithinRange($intervalStart, $intervalEnd)
            ->isFirstEmail($emailId)
            ->iterate();
    }

    public function getCompaniesFromCustomer(Customer $customer, int $offset = 0, int $limit = 0): array
    {
        $query = $this->repository->createSimpleBuilder()
            ->select('c')
            ->from(Company::class, 'c')
            ->where('c.customer = :customer')
            ->andWhere('c.deleted = 0')
            ->setParameter('customer', $customer)
            ->orderBy('c.companyId', 'DESC');

        if ($offset > 0) {
            $query->setFirstResult($offset);
        }

        if ($limit > 0) {
            $query->setMaxResults($limit);
        }

        return $query->getQuery()->getResult();
    }

    public function getCountOfCompaniesForCustomer(Customer $customer): int
    {
        return $this->repository->createSimpleBuilder()
            ->select('count(c.companyId)')
            ->from(Company::class, 'c')
            ->where('c.customer = :customer')
            ->andWhere('c.deleted = 0')
            ->setParameter('customer', $customer)
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function getActiveCompanies(): array
    {
        return $this->repository->createSimpleBuilder()
            ->select('c')
            ->from(Company::class, 'c')
            ->where('c.deleted = 0')
            ->getQuery()
            ->getResult();
    }

    public function getListBuilder(): QueryBuilder
    {
        return $this->repository->createSimpleBuilder()
            ->select('c')
            ->from(Company::class, 'c')
            ->leftJoin(Customer::class, 'cu', 'WITH', 'cu.customerId = c.customer')
            ->where('c.companyNumber IS NOT NULL')
            ->andWhere('cu.roleId = :role')
            ->andWhere('c.deleted = false')
            ->andWhere('c.hidden = false')
            ->andWhere('c.locked = false')
            ->setParameter('role', Customer::ROLE_NORMAL)
            ->orderBy('c.incorporationDate', 'DESC');
    }
}
