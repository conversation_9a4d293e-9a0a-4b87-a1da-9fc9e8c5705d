<?php

namespace CustomerModule\Repositories;

use CustomerModule\Queries\CustomerQueryBuilder;
use Entities\Customer;
use OrmModule\Repositories\IQueryableRepository;
use OrmModule\Repositories\IRepository;

class CustomerRepository
{
    /**
     * @var IRepository
     */
    private $repository;

    /**
     * @param IQueryableRepository|IRepository $repository
     */
    public function __construct(IQueryableRepository $repository)
    {
        $this->repository = $repository;
    }

    public function getCustomersForCreditExpiry(int $batchSize = 1000): iterable
    {
        $builder = new CustomerQueryBuilder($this->repository->createSimpleBuilder());
        $qb = $builder->getCustomers()
            ->withAvailableCredit()
            ->withPurchaseInactivity()
            ->getQueryBuilder()
            ->setMaxResults($batchSize)
            ->orderBy('c.customerId', 'ASC'); // Add ordering for consistent batching

        return (new CustomerQueryBuilder($qb))->iterate();
    }


}