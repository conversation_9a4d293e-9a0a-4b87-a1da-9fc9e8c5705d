# Doctrine SimpleObjectHydrator Performance Fix Plan

## Problem Analysis
The fatal error "Maximum execution time of 240 seconds exceeded" in SimpleObjectHydrator.php indicates that a query is returning an extremely large result set or has an infinite loop condition.

## Critical Issues Found

### 1. Queries Without LIMIT Clauses
- `CustomerRepository::getLaterThan()` - No limit, can return thousands of customers
- `CompanyRepository::getLaterThan()` - No limit, can return thousands of companies  
- `BaseRepository_Abstract::findAllIterator()` - Returns ALL entities
- `BaseRepository_Abstract::in()` - No limit on IN queries

### 2. Iterator Methods Without Pagination
- `getCustomersWithRequiredIdCheck()` - No limit
- `getCustomersWithCashBackWithoutCashBackLog()` - No limit
- `getSagepayCustomersWithActiveToken()` - No limit
- `getDeletedCompanies()` - No limit

## Immediate Solutions

### Phase 1: Add Default Limits (URGENT)
1. Add default LIMIT to all problematic queries
2. Implement batch processing for large datasets
3. Add proper pagination support

### Phase 2: Optimize Database Queries
1. Add missing database indexes
2. Optimize JOIN operations
3. Use EXPLAIN to identify slow queries

### Phase 3: Implement Proper Pagination
1. Replace unlimited iterators with paginated ones
2. Add batch size configuration
3. Implement memory-efficient processing

## Implementation Steps

### Step 1: Fix Critical Repository Methods
- Add default limits to `getLaterThan()` methods
- Implement batch processing for `findAllIterator()`
- Add pagination to iterator methods

### Step 2: Add Database Indexes
- Check for missing indexes on frequently queried columns
- Add indexes for date-based queries
- Optimize JOIN performance

### Step 3: Monitor and Test
- Add query logging to identify slow queries
- Implement performance monitoring
- Test with realistic data volumes

## Recommended Default Limits
- Iterator queries: 1000 records per batch
- Date-based queries: 5000 records max
- General queries: 100 records default

## Files to Modify
1. `project/models/Repositories/CustomerRepository.php`
2. `project/models/Repositories/CompanyRepository.php`
3. `project/models/Repositories/BaseRepository_Abstract.php`
4. `project/CustomerModule/Repositories/CustomerRepository.php`
5. `project/CompanyModule/Repositories/CompanyRepository.php`

## Emergency Workaround
If the issue is happening right now, you can:
1. Increase PHP max_execution_time temporarily
2. Add LIMIT clauses to the most problematic queries
3. Restart the web server to clear any stuck processes
