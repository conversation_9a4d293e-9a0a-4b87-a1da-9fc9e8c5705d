<?php

use Bootstrap\ApplicationLoader;
use TestModule\Boot;

define('DOCUMENT_ROOT', dirname(dirname(__FILE__)));
ini_set('memory_limit', '1G');
$config = require_once DOCUMENT_ROOT . '/project/bootstrap/default.php';
$config['config_path'] = DOCUMENT_ROOT . '/project/config/app/config.test.yml';
$config['environment'] = 'console';

$applicationLoader = new ApplicationLoader();
$container = $applicationLoader->load($config);
// legacy code depends on FApplication
$container->get('application');

error_reporting(E_ALL & ~E_DEPRECATED & ~E_USER_DEPRECATED);

Boot::setup($container);


