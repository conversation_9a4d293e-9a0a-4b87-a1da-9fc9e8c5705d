<?php

use Phinx\Migration\AbstractMigration;

class PerformanceIndexes extends AbstractMigration
{
    public function up()
    {
        // Add indexes to improve performance of frequently used queries
        
        // Customer table indexes
        $customersTable = $this->table('cms2_customers');
        if (!$this->hasIndex('cms2_customers', 'dtc')) {
            $customersTable->addIndex('dtc', ['name' => 'idx_customers_dtc']);
        }
        if (!$this->hasIndex('cms2_customers', 'isIdCheckRequired')) {
            $customersTable->addIndex('isIdCheckRequired', ['name' => 'idx_customers_id_check']);
        }
        $customersTable->save();
        
        // Company table indexes
        $companyTable = $this->table('cms2_company');
        if (!$this->hasIndex('cms2_company', 'dtc')) {
            $companyTable->addIndex('dtc', ['name' => 'idx_company_dtc']);
        }
        if (!$this->hasIndex('cms2_company', 'incorporationDate')) {
            $companyTable->addIndex('incorporationDate', ['name' => 'idx_company_incorporation_date']);
        }
        if (!$this->hasIndex('cms2_company', 'deleted')) {
            $companyTable->addIndex('deleted', ['name' => 'idx_company_deleted']);
        }
        $companyTable->save();
        
        // Cashback table indexes
        $cashbackTable = $this->table('cms2_cashbacks');
        if (!$this->hasIndex('cms2_cashbacks', ['customerId', 'statusId'])) {
            $cashbackTable->addIndex(['customerId', 'statusId'], ['name' => 'idx_cashbacks_customer_status']);
        }
        if (!$this->hasIndex('cms2_cashbacks', 'dtc')) {
            $cashbackTable->addIndex('dtc', ['name' => 'idx_cashbacks_dtc']);
        }
        $cashbackTable->save();
        
        // Orders table indexes
        $ordersTable = $this->table('cms2_orders');
        if (!$this->hasIndex('cms2_orders', 'dtc')) {
            $ordersTable->addIndex('dtc', ['name' => 'idx_orders_dtc']);
        }
        $ordersTable->save();
        
        // Customer logs table indexes (if exists)
        if ($this->hasTable('cms2_customer_logs')) {
            $customerLogsTable = $this->table('cms2_customer_logs');
            if (!$this->hasIndex('cms2_customer_logs', ['customerId', 'logType'])) {
                $customerLogsTable->addIndex(['customerId', 'logType'], ['name' => 'idx_customer_logs_customer_type']);
            }
            $customerLogsTable->save();
        }
        
        // Company tracking indexes (if exists)
        if ($this->hasTable('cms2_company_trackings')) {
            $trackingTable = $this->table('cms2_company_trackings');
            if (!$this->hasIndex('cms2_company_trackings', 'companyNumber')) {
                $trackingTable->addIndex('companyNumber', ['name' => 'idx_tracking_company_number']);
            }
            if (!$this->hasIndex('cms2_company_trackings', 'lastChecked')) {
                $trackingTable->addIndex('lastChecked', ['name' => 'idx_tracking_last_checked']);
            }
            if (!$this->hasIndex('cms2_company_trackings', 'dtExpires')) {
                $trackingTable->addIndex('dtExpires', ['name' => 'idx_tracking_expires']);
            }
            $trackingTable->save();
        }
    }
    
    public function down()
    {
        // Remove the indexes if needed
        
        $customersTable = $this->table('cms2_customers');
        if ($this->hasIndex('cms2_customers', 'dtc')) {
            $customersTable->removeIndex(['dtc']);
        }
        if ($this->hasIndex('cms2_customers', 'isIdCheckRequired')) {
            $customersTable->removeIndex(['isIdCheckRequired']);
        }
        $customersTable->save();
        
        $companyTable = $this->table('cms2_company');
        if ($this->hasIndex('cms2_company', 'dtc')) {
            $companyTable->removeIndex(['dtc']);
        }
        if ($this->hasIndex('cms2_company', 'incorporationDate')) {
            $companyTable->removeIndex(['incorporationDate']);
        }
        if ($this->hasIndex('cms2_company', 'deleted')) {
            $companyTable->removeIndex(['deleted']);
        }
        $companyTable->save();
        
        $cashbackTable = $this->table('cms2_cashbacks');
        if ($this->hasIndex('cms2_cashbacks', ['customerId', 'statusId'])) {
            $cashbackTable->removeIndex(['customerId', 'statusId']);
        }
        if ($this->hasIndex('cms2_cashbacks', 'dtc')) {
            $cashbackTable->removeIndex(['dtc']);
        }
        $cashbackTable->save();
        
        $ordersTable = $this->table('cms2_orders');
        if ($this->hasIndex('cms2_orders', 'dtc')) {
            $ordersTable->removeIndex(['dtc']);
        }
        $ordersTable->save();
        
        if ($this->hasTable('cms2_customer_logs')) {
            $customerLogsTable = $this->table('cms2_customer_logs');
            if ($this->hasIndex('cms2_customer_logs', ['customerId', 'logType'])) {
                $customerLogsTable->removeIndex(['customerId', 'logType']);
            }
            $customerLogsTable->save();
        }
        
        if ($this->hasTable('cms2_company_trackings')) {
            $trackingTable = $this->table('cms2_company_trackings');
            if ($this->hasIndex('cms2_company_trackings', 'companyNumber')) {
                $trackingTable->removeIndex(['companyNumber']);
            }
            if ($this->hasIndex('cms2_company_trackings', 'lastChecked')) {
                $trackingTable->removeIndex(['lastChecked']);
            }
            if ($this->hasIndex('cms2_company_trackings', 'dtExpires')) {
                $trackingTable->removeIndex(['dtExpires']);
            }
            $trackingTable->save();
        }
    }
    
    /**
     * Helper method to check if an index exists
     */
    private function hasIndex($tableName, $columns)
    {
        if (is_string($columns)) {
            $columns = [$columns];
        }
        
        try {
            $indexes = $this->fetchAll("SHOW INDEX FROM $tableName");
            $existingIndexes = [];
            
            foreach ($indexes as $index) {
                $keyName = $index['Key_name'];
                if (!isset($existingIndexes[$keyName])) {
                    $existingIndexes[$keyName] = [];
                }
                $existingIndexes[$keyName][] = $index['Column_name'];
            }
            
            foreach ($existingIndexes as $indexColumns) {
                if (array_diff($columns, $indexColumns) === [] && array_diff($indexColumns, $columns) === []) {
                    return true;
                }
            }
            
            return false;
        } catch (Exception $e) {
            // If we can't check, assume it doesn't exist
            return false;
        }
    }
}
